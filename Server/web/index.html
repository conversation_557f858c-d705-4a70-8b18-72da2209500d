<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Shake & Match</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --bg-primary: #0f1535;
            --bg-secondary: #1a1f3a;
            --bg-card: #242b48;
            --bg-hover: #2d3553;
            --text-primary: #ffffff;
            --text-secondary: #94a3b8;
            --text-muted: #64748b;
            --accent-purple: #8b5cf6;
            --accent-blue: #3b82f6;
            --accent-cyan: #06b6d4;
            --accent-green: #10b981;
            --accent-orange: #f97316;
            --accent-pink: #ec4899;
            --border-color: #2d3553;
            --gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-2: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
            --gradient-3: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .app-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Enhanced Sidebar */
        .sidebar {
            width: 260px;
            background: var(--bg-secondary);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: all 0.3s ease;
            border-right: 1px solid var(--border-color);
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: var(--accent-purple);
            border-radius: 2px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .logo-icon {
            width: 42px;
            height: 42px;
            background: var(--gradient-2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.5rem;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }

        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
            background: var(--gradient-2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-menu {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-section-title {
            padding: 0.5rem 1.5rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            color: var(--text-muted);
            font-weight: 600;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1.5rem;
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            font-size: 0.925rem;
            position: relative;
        }

        .nav-item:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
            padding-left: 2rem;
        }

        .nav-item.active {
            background: linear-gradient(90deg, rgba(139, 92, 246, 0.1) 0%, transparent 100%);
            color: var(--accent-purple);
            border-left: 3px solid var(--accent-purple);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 20px;
            background: var(--accent-purple);
            border-radius: 2px;
            opacity: 0.6;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: 260px;
            background: var(--bg-primary);
            min-height: 100vh;
        }

        /* Enhanced Top Bar */
        .top-bar {
            background: rgba(26, 31, 58, 0.8);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .top-left {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .page-breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .breadcrumb-separator {
            color: var(--text-muted);
        }

        .breadcrumb-current {
            color: var(--text-primary);
            font-weight: 500;
        }

        .search-bar {
            position: relative;
        }

        .search-input {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 0.625rem 1rem 0.625rem 2.5rem;
            border-radius: 10px;
            font-size: 0.875rem;
            width: 300px;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-purple);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            width: 350px;
        }

        .search-icon {
            position: absolute;
            left: 0.875rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            pointer-events: none;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .notifications-btn,
        .settings-btn {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .notifications-btn:hover,
        .settings-btn:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .notification-dot {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            background: var(--accent-pink);
            border-radius: 50%;
            border: 2px solid var(--bg-primary);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.875rem;
            padding: 0.5rem;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .user-info:hover {
            background: var(--bg-card);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--gradient-2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: 0 4px 10px rgba(139, 92, 246, 0.3);
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .user-role {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .content-area {
            padding: 2rem;
        }

        /* Welcome Section */
        .welcome-section {
            background: var(--gradient-2);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -10%;
            width: 500px;
            height: 500px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
        }

        .welcome-content {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .welcome-text h2 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            font-weight: 700;
        }

        .welcome-text p {
            opacity: 0.9;
            font-size: 1rem;
        }

        .welcome-stats {
            display: flex;
            gap: 3rem;
        }

        .welcome-stat {
            text-align: center;
        }

        .welcome-stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .welcome-stat-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .welcome-stat-change {
            font-size: 0.75rem;
            margin-top: 0.25rem;
            opacity: 0.8;
        }

        .positive-change {
            color: #86efac;
        }

        .negative-change {
            color: #fca5a5;
        }

        /* Enhanced Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--bg-card);
            border-radius: 16px;
            padding: 1.5rem;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .stat-card-info {
            flex: 1;
        }

        .stat-card-title {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .stat-card-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }

        .stat-card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-card-icon.users {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }

        .stat-card-icon.active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .stat-card-icon.revenue {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .stat-card-icon.growth {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        }

        .stat-card-footer {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
            font-size: 0.875rem;
        }

        .stat-change-indicator {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-weight: 600;
        }

        .stat-change-indicator.positive {
            color: var(--accent-green);
        }

        .stat-change-indicator.negative {
            color: #ef4444;
        }

        .stat-change-period {
            color: var(--text-muted);
        }

        /* Chart Sections */
        .chart-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .chart-card {
            background: var(--bg-card);
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .chart-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .chart-options {
            display: flex;
            gap: 0.5rem;
        }

        .chart-option-btn {
            padding: 0.375rem 0.875rem;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: var(--bg-hover);
            color: var(--text-secondary);
        }

        .chart-option-btn.active {
            background: var(--accent-purple);
            color: white;
        }

        .chart-container {
            height: 300px;
            position: relative;
        }

        /* Device Type Chart */
        .device-chart {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .circular-progress {
            position: relative;
            width: 200px;
            height: 200px;
            margin-bottom: 2rem;
        }

        .circular-progress svg {
            transform: rotate(-90deg);
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .progress-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .progress-label {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .device-legend {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            width: 100%;
        }

        .device-legend-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .device-legend-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .device-legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 3px;
        }

        .device-legend-value {
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Data Table Enhanced */
        .data-section {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            overflow: hidden;
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
            background: rgba(36, 43, 72, 0.5);
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 1rem 1.5rem;
            text-align: left;
        }

        th {
            background: rgba(36, 43, 72, 0.3);
            color: var(--text-muted);
            font-weight: 600;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 1px solid var(--border-color);
        }

        td {
            color: var(--text-primary);
            border-bottom: 1px solid rgba(45, 53, 83, 0.5);
        }

        tr:hover td {
            background: rgba(139, 92, 246, 0.05);
        }

        /* Enhanced Badges */
        .badge {
            padding: 0.375rem 0.875rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .badge-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
        }

        .badge-success {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .badge-success .badge-dot {
            background: #10b981;
        }

        .badge-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .badge-warning .badge-dot {
            background: #f59e0b;
        }

        .badge-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .badge-danger .badge-dot {
            background: #ef4444;
        }

        .badge-info {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .badge-info .badge-dot {
            background: #3b82f6;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .chart-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .welcome-stats {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Enhanced Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="logo-icon">S</div>
                    <div class="logo-text">Shake&Match</div>
                </div>
            </div>
            <nav class="nav-menu">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <button class="nav-item active" onclick="showSection('dashboard')">
                        <div class="nav-icon">📊</div>
                        Dashboard
                    </button>
                    <button class="nav-item" onclick="showSection('analytics')">
                        <div class="nav-icon">📈</div>
                        Analytics
                    </button>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <button class="nav-item" onclick="showSection('users')">
                        <div class="nav-icon">👥</div>
                        Users
                    </button>
                    <button class="nav-item" onclick="showSection('matches')">
                        <div class="nav-icon">❤️</div>
                        Matches
                    </button>
                    <button class="nav-item" onclick="showSection('messages')">
                        <div class="nav-icon">💬</div>
                        Messages
                    </button>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Settings</div>
                    <button class="nav-item" onclick="showSection('profile')">
                        <div class="nav-icon">👤</div>
                        Profile
                    </button>
                    <button class="nav-item" onclick="showSection('settings')">
                        <div class="nav-icon">⚙️</div>
                        Settings
                    </button>
                    <button class="nav-item" onclick="logout()">
                        <div class="nav-icon">🚪</div>
                        Logout
                    </button>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Enhanced Top Bar -->
            <div class="top-bar">
                <div class="top-left">
                    <div class="page-breadcrumb">
                        <span>Dashboard</span>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-current">Analytics</span>
                    </div>
                    <div class="search-bar">
                        <span class="search-icon">🔍</span>
                        <input type="text" class="search-input" placeholder="Search...">
                    </div>
                </div>
                <div class="user-menu">
                    <button class="notifications-btn">
                        <span>🔔</span>
                        <span class="notification-dot"></span>
                    </button>
                    <button class="settings-btn">
                        <span>⚙️</span>
                    </button>
                    <div class="user-info">
                        <div class="user-avatar" id="userAvatar">A</div>
                        <div class="user-details">
                            <div class="user-name" id="currentUsername">Admin</div>
                            <div class="user-role">Administrator</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-area">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="content-section">
                    <!-- Welcome Section -->
                    <div class="welcome-section">
                        <div class="welcome-content">
                            <div class="welcome-text">
                                <h2>Welcome back, <span id="welcomeUsername">Admin</span>! 👋</h2>
                                <p>Here's what's happening with your platform today.</p>
                            </div>
                            <div class="welcome-stats">
                                <div class="welcome-stat">
                                    <div class="welcome-stat-value">$65.4K</div>
                                    <div class="welcome-stat-label">Today's Revenue</div>
                                    <div class="welcome-stat-change positive-change">↑ 12.5%</div>
                                </div>
                                <div class="welcome-stat">
                                    <div class="welcome-stat-value">78.4%</div>
                                    <div class="welcome-stat-label">Growth Rate</div>
                                    <div class="welcome-stat-change positive-change">↑ 8.2%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Stats Grid -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-card-header">
                                <div class="stat-card-info">
                                    <div class="stat-card-title">Active Users</div>
                                    <div class="stat-card-value" id="activeUsers">42.5K</div>
                                </div>
                                <div class="stat-card-icon users">👥</div>
                            </div>
                            <div class="stat-card-footer">
                                <span class="stat-change-indicator positive">
                                    <span>↑</span>
                                    <span>12%</span>
                                </span>
                                <span class="stat-change-period">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-header">
                                <div class="stat-card-info">
                                    <div class="stat-card-title">Total Users</div>
                                    <div class="stat-card-value" id="totalUsers">97.4K</div>
                                </div>
                                <div class="stat-card-icon active">✨</div>
                            </div>
                            <div class="stat-card-footer">
                                <span class="stat-change-indicator positive">
                                    <span>↑</span>
                                    <span>18%</span>
                                </span>
                                <span class="stat-change-period">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-header">
                                <div class="stat-card-info">
                                    <div class="stat-card-title">Total Matches</div>
                                    <div class="stat-card-value" id="totalMatches">82.7K</div>
                                </div>
                                <div class="stat-card-icon revenue">💕</div>
                            </div>
                            <div class="stat-card-footer">
                                <span class="stat-change-indicator positive">
                                    <span>↑</span>
                                    <span>24%</span>
                                </span>
                                <span class="stat-change-period">from last month</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-header">
                                <div class="stat-card-info">
                                    <div class="stat-card-title">Total Views</div>
                                    <div class="stat-card-value">68.4K</div>
                                </div>
                                <div class="stat-card-icon growth">📊</div>
                            </div>
                            <div class="stat-card-footer">
                                <span class="stat-change-indicator positive">
                                    <span>↑</span>
                                    <span>35%</span>
                                </span>
                                <span class="stat-change-period">from last month</span>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Section -->
                    <div class="chart-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">Monthly Revenue</h3>
                                <div class="chart-options">
                                    <button class="chart-option-btn active">Week</button>
                                    <button class="chart-option-btn">Month</button>
                                    <button class="chart-option-btn">Year</button>
                                </div>
                            </div>
                            <div class="chart-container" id="revenueChart">
                                <!-- Chart will be rendered here -->
                                <div style="display: flex; align-items: flex-end; justify-content: space-around; height: 100%; padding: 20px;">
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 40%; border-radius: 4px 4px 0 0;"></div>
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 70%; border-radius: 4px 4px 0 0;"></div>
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 60%; border-radius: 4px 4px 0 0;"></div>
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 90%; border-radius: 4px 4px 0 0;"></div>
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 50%; border-radius: 4px 4px 0 0;"></div>
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 45%; border-radius: 4px 4px 0 0;"></div>
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 65%; border-radius: 4px 4px 0 0;"></div>
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 55%; border-radius: 4px 4px 0 0;"></div>
                                    <div style="background: linear-gradient(180deg, var(--accent-cyan) 0%, var(--accent-blue) 100%); width: 30px; height: 40%; border-radius: 4px 4px 0 0;"></div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; padding: 0 20px; margin-top: 10px;">
                                <span style="font-size: 0.75rem; color: var(--text-muted);">Average monthly engagement for active users</span>
                                <span style="font-size: 1.5rem; color: var(--accent-cyan); font-weight: 700;">68.9% <span style="font-size: 0.875rem; color: var(--accent-green);">↑ 11.0%</span></span>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">Device Type</h3>
                            </div>
                            <div class="device-chart">
                                <div class="circular-progress">
                                    <svg width="200" height="200">
                                        <circle cx="100" cy="100" r="90" stroke="var(--bg-hover)" stroke-width="10" fill="none"></circle>
                                        <circle cx="100" cy="100" r="90" stroke="url(#grad1)" stroke-width="10" fill="none" stroke-dasharray="452" stroke-dashoffset="113" stroke-linecap="round"></circle>
                                        <defs>
                                            <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                                                <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                                <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
                                                <stop offset="100%" style="stop-color:#ec4899;stop-opacity:1" />
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                    <div class="progress-text">
                                        <div class="progress-value">68%</div>
                                        <div class="progress-label">Total Views</div>
                                    </div>
                                </div>
                                <div class="device-legend">
                                    <div class="device-legend-item">
                                        <div class="device-legend-label">
                                            <span class="device-legend-dot" style="background: #3b82f6;"></span>
                                            Desktop
                                        </div>
                                        <div class="device-legend-value">35%</div>
                                    </div>
                                    <div class="device-legend-item">
                                        <div class="device-legend-label">
                                            <span class="device-legend-dot" style="background: #8b5cf6;"></span>
                                            Tablet
                                        </div>
                                        <div class="device-legend-value">48%</div>
                                    </div>
                                    <div class="device-legend-item">
                                        <div class="device-legend-label">
                                            <span class="device-legend-dot" style="background: #ec4899;"></span>
                                            Mobile
                                        </div>
                                        <div class="device-legend-value">27%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Section -->
                <div id="users-section" class="content-section" style="display: none;">
                    <div class="data-section">
                        <div class="section-header">
                            <h2 class="chart-title">User Management</h2>
                            <div class="chart-options">
                                <input type="text" class="search-input" placeholder="Search users..." id="userSearch">
                                <button class="chart-option-btn active" onclick="showCreateUserModal()">
                                    + Add User
                                </button>
                            </div>
                        </div>
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <tr>
                                        <td><strong>JohnDoe</strong></td>
                                        <td><EMAIL></td>
                                        <td><span class="badge badge-info"><span class="badge-dot"></span>USER</span></td>
                                        <td><span class="badge badge-success"><span class="badge-dot"></span>ACTIVE</span></td>
                                        <td>2024-01-15</td>
                                        <td>
                                            <button class="chart-option-btn">Edit</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = window.location.origin;
        
        // Global data storage
        let users = [];
        let matches = [];
        let messages = [];
        let activeUsersData = [];
        let currentUser = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            checkAuthentication();
        });

        // Check authentication status
        async function checkAuthentication() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/auth/status`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    window.location.href = '/admin/login';
                    return;
                }

                const data = await response.json();
                currentUser = data.user;

                // Update header with user info
                document.getElementById('currentUsername').textContent = currentUser.username;
                document.getElementById('welcomeUsername').textContent = currentUser.username;
                document.getElementById('userAvatar').textContent = currentUser.username.charAt(0).toUpperCase();

                // Load dashboard data
                loadDashboardData();
                setInterval(loadDashboardData, 30000);

            } catch (error) {
                console.error('Authentication check failed:', error);
                window.location.href = '/admin/login';
            }
        }

        // Logout function
        async function logout() {
            try {
                await fetch(`${API_BASE}/api/admin/logout`, {
                    method: 'POST',
                    credentials: 'include'
                });
                window.location.href = '/admin/login';
            } catch (error) {
                console.error('Logout error:', error);
                window.location.href = '/admin/login';
            }
        }

        // Show section
        function showSection(sectionName) {
            // Update nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update breadcrumb
            const breadcrumb = document.querySelector('.breadcrumb-current');
            const titles = {
                'dashboard': 'Overview',
                'analytics': 'Analytics',
                'users': 'User Management',
                'matches': 'Matches',
                'messages': 'Messages',
                'profile': 'Profile',
                'settings': 'Settings'
            };
            if (breadcrumb) {
                breadcrumb.textContent = titles[sectionName] || 'Dashboard';
            }

            // Show/hide sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });
            const section = document.getElementById(`${sectionName}-section`);
            if (section) {
                section.style.display = 'block';
            } else {
                // Default to dashboard if section doesn't exist yet
                document.getElementById('dashboard-section').style.display = 'block';
            }
        }

        // Load all dashboard data
        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadUsers(),
                    loadMatches(),
                    loadMessages(),
                    loadActiveUsers()
                ]);
                updateStats();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load users data
        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/api/admin/users`, {
                    credentials: 'include'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                users = data.users || [];
                renderUsersTable();
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        // Render users table
        function renderUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            if (!tbody) return;

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: var(--text-muted);">No users found</td></tr>';
                return;
            }

            tbody.innerHTML = users.map(user => `
                <tr>
                    <td><strong>${user.username}</strong></td>
                    <td>${user.email || '-'}</td>
                    <td><span class="badge badge-info"><span class="badge-dot"></span>${(user.role || 'user').toUpperCase()}</span></td>
                    <td><span class="badge badge-success"><span class="badge-dot"></span>${(user.accountStatus || 'active').toUpperCase()}</span></td>
                    <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                    <td>
                        <button class="chart-option-btn" onclick="editUser('${user.id}')">Edit</button>
                    </td>
                </tr>
            `).join('');
        }

        // Placeholder functions
        function loadMatches() { matches = []; }
        function loadMessages() { messages = []; }
        function loadActiveUsers() { activeUsersData = []; }
        function showCreateUserModal() { alert('Create user modal - to be implemented'); }
        function editUser(id) { alert('Edit user - to be implemented'); }

        // Update statistics
        function updateStats() {
            const totalUsersEl = document.getElementById('totalUsers');
            const activeUsersEl = document.getElementById('activeUsers');
            const totalMatchesEl = document.getElementById('totalMatches');
            
            if (totalUsersEl) totalUsersEl.textContent = users.length > 0 ? `${users.length}` : '0';
            if (activeUsersEl) activeUsersEl.textContent = activeUsersData.length > 0 ? `${activeUsersData.length}` : '0';
            if (totalMatchesEl) totalMatchesEl.textContent = matches.length > 0 ? `${matches.length}` : '0';
        }
    </script>
</body>
</html>